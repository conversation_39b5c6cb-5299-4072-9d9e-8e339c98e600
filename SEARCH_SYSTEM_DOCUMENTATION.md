# Advanced Search and Filtering System Documentation

## Overview

The Communiserve platform now includes a comprehensive search and filtering system that allows users to efficiently find and filter data across all major entities: activities, tasks, reports, users, and locations.

## Features

### 1. Global Search
- **Endpoint**: `/api/v1/search/global`
- **Frontend**: `/dashboard/search`
- **Description**: Search across all entity types simultaneously with unified results

### 2. Entity-Specific Search
- **Activities**: Enhanced search in activities page with advanced filters
- **Tasks**: Enhanced search in tasks tab with advanced filters  
- **Reports**: Enhanced search capabilities (existing endpoints)
- **Users**: New dedicated search endpoint `/api/v1/users/search`
- **Locations**: New dedicated search endpoint `/api/v1/locations/search`

### 3. Advanced Filtering
- Text search across multiple fields
- Date range filtering
- Status-based filtering
- Location-based filtering
- Role-based filtering
- Cost and participant range filtering
- Multi-select filters with tags

## Backend Implementation

### New DTOs Created

#### 1. Search Users DTO (`api/src/users/dto/search-users.dto.ts`)
```typescript
export namespace SearchUsersDto {
  export class Input extends PaginationDto {
    @IsOptional()
    @IsString()
    q?: string;

    @IsOptional()
    @IsEnum(UserRole)
    role?: UserRole;

    @IsOptional()
    @IsArray()
    @IsEnum(UserRole, { each: true })
    roles?: UserRole[];

    // ... additional filters
  }

  export class UserItem {
    @Expose()
    id: string;

    @Expose()
    names: string;

    @Expose()
    email: string;

    // ... additional fields
  }

  export class Output extends PaginatedResponseDto<UserItem> {}
}
```

#### 2. Search Locations DTO (`api/src/locations/dto/search-locations.dto.ts`)
```typescript
export enum LocationType {
  PROVINCE = 'PROVINCE',
  DISTRICT = 'DISTRICT',
  SECTOR = 'SECTOR',
  CELL = 'CELL',
  VILLAGE = 'VILLAGE',
  HOUSE = 'HOUSE',
  ISIBO = 'ISIBO',
}

export namespace SearchLocationsDto {
  export class Input extends PaginationDto {
    @IsOptional()
    @IsString()
    q?: string;

    @IsOptional()
    @IsEnum(LocationType)
    type?: LocationType;

    @IsOptional()
    @IsArray()
    @IsEnum(LocationType, { each: true })
    types?: LocationType[];

    // ... additional filters
  }

  export class LocationSearchResult {
    @Expose()
    id: string;

    @Expose()
    name: string;

    @Expose()
    type: LocationType;

    // ... additional fields
  }

  export class Output extends PaginatedResponseDto<LocationSearchResult> {}
}
```

#### 3. Enhanced Activity/Task/Report DTOs
Enhanced existing DTOs with additional search parameters:
- Date range filtering
- Status filtering
- Location filtering
- Cost/participant range filtering

### New Services Created

#### 1. Users Search Service
Enhanced `UsersService` with `searchUsers` method:
- Full-text search across names, email, phone
- Role-based filtering
- Location-based filtering
- Date range filtering
- Active/inactive status filtering

#### 2. Locations Search Service (`api/src/locations/search-locations.service.ts`)
- Cross-location-type search
- Hierarchical filtering
- Population-based filtering
- Leader-based filtering
- Relevance scoring

#### 3. Global Search Service (`api/src/__shared__/services/global-search.service.ts`)
- Unified search across all entities
- Result aggregation and ranking
- Performance optimization
- Metadata extraction

### New Controllers

#### 1. Enhanced Users Controller
Added search endpoint:
```typescript
@GetOperation("search", "Search users with advanced filters")
@PaginatedOkResponse(SearchUsersDto.UserItem)
@IsAuthorized()
async searchUsers(
  @Query() searchDto: SearchUsersDto.Input,
): Promise<SearchUsersDto.Output> {
  return this.usersService.searchUsers(searchDto);
}
```

#### 2. Search Locations Controller (`api/src/locations/search-locations.controller.ts`)
Dedicated controller for location search with advanced filtering.

#### 3. Global Search Controller (`api/src/__shared__/controllers/global-search.controller.ts`)
Handles unified search across all entities.

## Frontend Implementation

### New Components Created

#### 1. Advanced Search Form (`webui/components/search/advanced-search-form.tsx`)
Reusable component with features:
- Dynamic filter configuration
- Multiple filter types (text, select, multiselect, date-range, number-range)
- Real-time filter management
- Sorting options
- Reset functionality

#### 2. Search Results (`webui/components/search/search-results.tsx`)
Unified results display with:
- Type-based icons and colors
- Metadata display
- Pagination
- Relevance scoring
- Action buttons

#### 3. Date Range Picker (`webui/components/ui/date-range-picker.tsx`)
Calendar-based date range selection component.

### New Pages Created

#### 1. Global Search Page (`webui/app/dashboard/search/page.tsx`)
Features:
- Entity type filtering
- Tabbed results view
- Search performance metrics
- Real-time search

### Enhanced Existing Pages

#### 1. Activities Page (`webui/app/dashboard/activities/page.tsx`)
- Replaced simple search with advanced search form
- Added village, status, date range, and type filters
- Integrated with new search API

#### 2. Tasks Tab (`webui/app/dashboard/activities/tasks-tab.tsx`)
- Added advanced search capabilities
- Cost and participant range filtering
- Activity and isibo filtering
- Date range filtering

### New API Layer (`webui/lib/api/search.ts`)
Comprehensive API client with:
- Type-safe search parameters
- Response transformation
- Error handling
- Metadata extraction utilities

## Search Capabilities by Entity

### Activities
- **Text Search**: Title, description
- **Filters**: Village, status, date range, activity type, organizer
- **Sorting**: Created date, updated date, title
- **Metadata**: Village, cell, date, status

### Tasks
- **Text Search**: Title, description
- **Filters**: Activity, status, isibo, cost range, participant range, date range
- **Sorting**: Created date, updated date, title, cost
- **Metadata**: Activity, isibo, status, estimated/actual cost

### Reports
- **Text Search**: Title, description, comments
- **Filters**: Activity, task, isibo, cost range, participant range, evidence status
- **Sorting**: Created date, updated date
- **Metadata**: Task, activity, isibo, evidence status

### Users
- **Text Search**: Names, email, phone
- **Filters**: Role, location (village/cell/house), active status, date range
- **Sorting**: Created date, updated date, names
- **Metadata**: Role, email, phone, village, cell

### Locations
- **Text Search**: Name
- **Filters**: Type, parent location, population range, date range
- **Sorting**: Created date, updated date, name
- **Metadata**: Type, parent location, population, leader

## Performance Optimizations

### Backend
1. **Database Indexing**: Added indexes on searchable fields
2. **Query Optimization**: Efficient JOIN strategies and WHERE clauses
3. **Pagination**: Consistent pagination across all search endpoints
4. **Caching**: Result caching for frequently accessed data

### Frontend
1. **Debounced Search**: Prevents excessive API calls
2. **Lazy Loading**: Components load data as needed
3. **Memoization**: React.memo and useMemo for performance
4. **Virtual Scrolling**: For large result sets

## Security Considerations

### Authorization
- All search endpoints require authentication
- Role-based access control (RBAC) applied
- Isibo leaders can only see their assigned data
- Data filtering based on user permissions

### Data Protection
- Input validation and sanitization
- SQL injection prevention
- Rate limiting on search endpoints
- Audit logging for search activities

## Usage Examples

### Global Search
```typescript
// Search across all entities
const results = await searchAPI.globalSearch({
  q: "community meeting",
  entities: ['activities', 'tasks', 'reports'],
  page: 1,
  size: 20
});
```

### Advanced Activity Search
```typescript
// Search activities with filters
const activities = await searchAPI.searchActivities({
  q: "health",
  villageIds: ["village-1", "village-2"],
  status: "IN_PROGRESS",
  dateFrom: "2024-01-01",
  dateTo: "2024-12-31",
  sortBy: "createdAt",
  sortOrder: "DESC"
});
```

### User Search
```typescript
// Search users by role and location
const users = await searchAPI.searchUsers({
  q: "john",
  roles: ["VILLAGE_LEADER", "CELL_LEADER"],
  villageIds: ["village-1"],
  isActive: true
});
```

## Future Enhancements

### Planned Features
1. **Elasticsearch Integration**: For advanced full-text search
2. **Search Analytics**: Track search patterns and optimize
3. **Saved Searches**: Allow users to save frequently used searches
4. **Search Suggestions**: Auto-complete and search suggestions
5. **Export Functionality**: Export search results to various formats

### Performance Improvements
1. **Search Result Caching**: Redis-based caching
2. **Background Indexing**: Async search index updates
3. **Search Result Ranking**: ML-based relevance scoring
4. **Faceted Search**: Dynamic filter options based on results

## Troubleshooting

### Common Issues
1. **Slow Search Performance**: Check database indexes and query optimization
2. **Empty Results**: Verify user permissions and data availability
3. **Filter Not Working**: Check DTO validation and service implementation
4. **Frontend Errors**: Verify API endpoint availability and response format

### Debugging
1. Enable debug logging in search services
2. Use browser dev tools to inspect API calls
3. Check database query performance
4. Verify user permissions and role assignments

## Conclusion

The advanced search and filtering system provides a comprehensive solution for data discovery across the Communiserve platform. It enhances user experience by making information easily accessible while maintaining security and performance standards.
