import { NestFactory } from '@nestjs/core';
import { AppModule } from '../app.module';
import { DataSource } from 'typeorm';
import { seedData } from './seed-data';
import * as bcrypt from 'bcryptjs';

// Import entities
import { Province } from '../locations/entities/province.entity';
import { District } from '../locations/entities/district.entity';
import { Sector } from '../locations/entities/sector.entity';
import { Cell } from '../locations/entities/cell.entity';
import { Village } from '../locations/entities/village.entity';
import { Isibo } from '../locations/entities/isibo.entity';
import { House } from '../locations/entities/house.entity';
import { User } from '../users/entities/user.entity';
import { Activity } from '../activities/entities/activity.entity';
import { Task } from '../activities/entities/task.entity';
import { Report } from '../activities/entities/report.entity';
import { LocationSeedService } from '../__shared__/seed/location-seed.service';
import { UserRole } from '../__shared__/enums/user-role.enum';
import { ETaskStatus } from '../activities/enum/ETaskStatus';

async function seed() {
  console.log('🌱 Starting database seeding...');
  
  const app = await NestFactory.createApplicationContext(AppModule);
  const dataSource = app.get(DataSource);
  const locationSeedService = app.get(LocationSeedService);

  try {
    // Clear existing data (in reverse order of dependencies)
    console.log('🧹 Clearing existing data...');
    await dataSource.query('DELETE FROM report_attendance');
    await dataSource.query('DELETE FROM reports');
    await dataSource.query('DELETE FROM tasks');
    await dataSource.query('DELETE FROM activities');

    // Clear leader references first to avoid foreign key constraints
    await dataSource.query('UPDATE isibos SET leader_id = NULL WHERE leader_id IS NOT NULL');
    await dataSource.query('UPDATE villages SET leader_id = NULL WHERE leader_id IS NOT NULL');
    await dataSource.query('UPDATE cells SET leader_id = NULL WHERE leader_id IS NOT NULL');

    // Now we can safely delete users (except admins)
    await dataSource.query('DELETE FROM users WHERE role != \'ADMIN\'');
    await dataSource.query('DELETE FROM houses');
    await dataSource.query('DELETE FROM isibos');
    await dataSource.query('DELETE FROM villages');
    await dataSource.query('DELETE FROM cells');
    await dataSource.query('DELETE FROM sectors');
    await dataSource.query('DELETE FROM districts');
    await dataSource.query('DELETE FROM provinces');

    // First, seed the real Rwanda location data
    console.log('🗺️ Seeding Rwanda location data...');
    await locationSeedService.run();

    // Find Gihanga village and get its cell
    console.log('🔍 Finding Gihanga village...');
    const gihangaVillage = await dataSource.getRepository(Village).findOne({
      where: { name: 'GIHANGA' },
      relations: ['cell']
    });

    if (!gihangaVillage) {
      throw new Error('Gihanga village not found in the seeded data');
    }

    console.log(`✓ Found Gihanga village in cell: ${gihangaVillage.cell.name}`);

    // Get all villages in the same cell as Gihanga
    const cellVillages = await dataSource.getRepository(Village).find({
      where: { cell: { id: gihangaVillage.cell.id } },
      relations: ['cell']
    });

    console.log(`✓ Found ${cellVillages.length} villages in cell ${gihangaVillage.cell.name}`);

    // Get repositories
    const provinceRepo = dataSource.getRepository(Province);
    const districtRepo = dataSource.getRepository(District);
    const sectorRepo = dataSource.getRepository(Sector);
    const cellRepo = dataSource.getRepository(Cell);
    const villageRepo = dataSource.getRepository(Village);
    const isiboRepo = dataSource.getRepository(Isibo);
    const houseRepo = dataSource.getRepository(House);
    const userRepo = dataSource.getRepository(User);
    const activityRepo = dataSource.getRepository(Activity);
    const taskRepo = dataSource.getRepository(Task);
    const reportRepo = dataSource.getRepository(Report);

    // Now use the villages from Gihanga's cell for creating isibos
    console.log('🏘️ Using villages from Gihanga\'s cell for isibo creation...');
    const villages = new Map<string, Village>();
    cellVillages.forEach(village => {
      villages.set(village.name, village);
      console.log(`   ✓ Using village: ${village.name}`);
    });

    // 6. Create Isibos (10 per village)
    console.log('🏘️ Creating isibos...');
    const isibos = new Map<string, Isibo>();
    for (const village of cellVillages) {
      for (let i = 1; i <= 10; i++) {
        const isiboName = `${village.name} ISIBO ${i}`;
        const isibo = isiboRepo.create({
          name: isiboName,
          village: village,
        });
        const savedIsibo = await isiboRepo.save(isibo);
        isibos.set(isiboName, savedIsibo);
        console.log(`   ✓ Created isibo: ${isiboName}`);
      }
    }

    // 7. Create Houses (10 per isibo)
    console.log('🏠 Creating houses...');
    const houses = new Map<string, House>();
    let houseCounter = 1;
    for (const [isiboName, isibo] of isibos) {
      for (let i = 1; i <= 10; i++) {
        const houseCode = `H${String(houseCounter).padStart(3, '0')}-${String(i).padStart(2, '0')}`;
        const house = houseRepo.create({
          code: houseCode,
          address: `House ${i}, ${isiboName}, ${isibo.village.name}`,
          isibo: isibo,
        });
        const savedHouse = await houseRepo.save(house);
        houses.set(houseCode, savedHouse);
        console.log(`   ✓ Created house: ${houseCode}`);
      }
      houseCounter++;
    }

    // 8. Create Citizens (3 per house)
    console.log('👥 Creating citizens...');
    const users = new Map<string, User>();
    let citizenCounter = 1;

    // Get sample names from seedData
    const firstNames = ['Jean', 'Marie', 'Pierre', 'Agnes', 'Emmanuel', 'Claudine', 'Innocent', 'Esperance', 'Damascene', 'Vestine'];
    const lastNames = ['UWIMANA', 'MUKAMANA', 'NIYONZIMA', 'UWIMANA', 'HABIMANA', 'MUKAMANA', 'NZEYIMANA', 'UWIMANA', 'MUKAMANA', 'NIYONZIMA'];

    for (const [houseCode, house] of houses) {
      for (let i = 1; i <= 3; i++) {
        const firstName = firstNames[Math.floor(Math.random() * firstNames.length)];
        const lastName = lastNames[Math.floor(Math.random() * lastNames.length)];
        const names = `${firstName} ${lastName}`;

        const hashedPassword = await bcrypt.hash('password123', 10);

        const user = userRepo.create({
          names,
          email: `${firstName.toLowerCase()}.${lastName.toLowerCase()}${citizenCounter}.${i}@example.com`,
          phone: `+25078${String(citizenCounter * 3 + i).padStart(7, '0')}`,
          password: hashedPassword,
          role: UserRole.CITIZEN,
          village: house.isibo.village,
          isibo: house.isibo,
          house: house,
          cell: gihangaVillage.cell,
        });

        const savedUser = await userRepo.save(user);
        users.set(user.email, savedUser);
        console.log(`   ✓ Created citizen: ${names}`);
      }
      citizenCounter++;
    }

    console.log(`✅ Created ${users.size} citizens`);

    // 9. Create Activities (50 total, distributed across villages)
    console.log('🎯 Creating activities...');
    const activities = new Map<string, Activity>();

    // Activity types
    const activityTypes = [
      'Community Health Campaign', 'Education Support Program', 'Agricultural Training Workshop',
      'Community Infrastructure Repair', 'Women Empowerment Program', 'Youth Sports Tournament',
      'Maternal Health Program', 'Child Nutrition Campaign', 'Elderly Care Initiative',
      'Disability Support Services', 'Mental Health Awareness', 'HIV/AIDS Prevention',
      'Malaria Prevention Campaign', 'Vaccination Drive', 'Family Planning Education',
      'Emergency Preparedness Training', 'Disaster Response Drill', 'First Aid Training',
      'Road Safety Campaign', 'Fire Safety Education', 'Home Security Awareness',
      'Financial Literacy Program', 'Savings Group Formation', 'Credit Union Development',
      'Cooperative Formation', 'Market Access Program', 'Value Chain Development',
      'Livestock Management Training', 'Crop Diversification Program', 'Irrigation System Setup',
      'Soil Conservation Project', 'Seed Distribution Program', 'Fertilizer Education',
      'Pest Control Training', 'Harvest Management', 'Post-Harvest Processing',
      'Community Garden Project', 'School Feeding Program', 'Adult Literacy Classes',
      'Vocational Skills Training', 'Leadership Development', 'Conflict Resolution Training',
      'Peace Building Initiative', 'Cultural Preservation', 'Traditional Crafts Workshop',
      'Music and Arts Program', 'Community Library Setup'
    ];

    // Generate random dates between January and June 2025
    const getRandomDate = (start: Date, end: Date): Date => {
      return new Date(start.getTime() + Math.random() * (end.getTime() - start.getTime()));
    };
    const startDate = new Date('2025-01-01');
    const endDate = new Date('2025-06-30');

    for (let i = 0; i < 50; i++) {
      const activityType = activityTypes[i % activityTypes.length];
      const randomVillage = cellVillages[Math.floor(Math.random() * cellVillages.length)];
      const uniqueTitle = `${activityType} - ${randomVillage.name}`;

      const activity = activityRepo.create({
        title: uniqueTitle,
        description: `Community initiative focused on ${activityType.toLowerCase()} for the benefit of all residents in ${randomVillage.name}`,
        date: getRandomDate(startDate, endDate),
        village: randomVillage,
      });

      const savedActivity = await activityRepo.save(activity);
      activities.set(uniqueTitle, savedActivity);
      console.log(`   ✓ Created activity: ${uniqueTitle}`);
    }

    console.log(`✅ Created ${activities.size} activities`);

    // 10. Create Tasks (1-3 tasks per activity)
    console.log('📋 Creating tasks...');
    const tasks = new Map<string, Task>();
    const usedCombinations = new Set<string>();

    for (const [activityTitle, activity] of activities) {
      // Get isibos from the same village as the activity
      const villageIsibos = Array.from(isibos.values()).filter(isibo =>
        isibo.village.id === activity.village.id
      );

      // Create 1-3 tasks per activity
      const numTasks = Math.min(Math.floor(Math.random() * 3) + 1, villageIsibos.length);
      const shuffledIsibos = [...villageIsibos].sort(() => 0.5 - Math.random());

      let tasksCreated = 0;
      for (let i = 0; i < shuffledIsibos.length && tasksCreated < numTasks; i++) {
        const assignedIsibo = shuffledIsibos[i];
        const combinationKey = `${activityTitle}|${assignedIsibo.name}`;

        // Skip if this combination already exists
        if (usedCombinations.has(combinationKey)) {
          continue;
        }

        usedCombinations.add(combinationKey);

        const estimatedCost = Math.floor(Math.random() * 500000) + 50000; // 50k - 550k RWF
        const expectedParticipants = Math.floor(Math.random() * 50) + 10; // 10-60 participants
        const expectedFinancialImpact = Math.floor(Math.random() * 1000000) + 100000; // 100k - 1.1M RWF

        // 70% chance of having a report (completed), 30% pending
        const hasReport = Math.random() < 0.7;
        const actualCost = hasReport ? Math.floor(estimatedCost * (0.8 + Math.random() * 0.4)) : 0;
        const actualParticipants = hasReport ? Math.floor(expectedParticipants * (0.7 + Math.random() * 0.6)) : 0;
        const actualFinancialImpact = hasReport ? Math.floor(expectedFinancialImpact * (0.6 + Math.random() * 0.8)) : 0;

        const taskTitle = `${activityTitle} - Task ${tasksCreated + 1}`;
        const task = taskRepo.create({
          title: taskTitle,
          description: `Implementation of ${activityTitle.toLowerCase()} in ${assignedIsibo.name}`,
          status: hasReport ? ETaskStatus.COMPLETED : ETaskStatus.PENDING,
          estimatedCost,
          actualCost,
          expectedParticipants,
          actualParticipants,
          expectedFinancialImpact,
          actualFinancialImpact,
          activity: activity,
          isibo: assignedIsibo,
        });

        const savedTask = await taskRepo.save(task);
        tasks.set(taskTitle, savedTask);
        console.log(`   ✓ Created task: ${taskTitle}`);
        tasksCreated++;
      }
    }

    console.log(`✅ Created ${tasks.size} tasks`);

    // 11. Create Reports (for completed tasks only)
    console.log('📊 Creating reports...');
    let reportCount = 0;

    for (const [taskTitle, task] of tasks) {
      // Only create reports for completed tasks
      if (task.status !== ETaskStatus.COMPLETED) {
        continue;
      }

      // Get users from the same isibo as the task for attendance
      const isiboUsers = Array.from(users.values()).filter(user =>
        user.isibo.id === task.isibo.id
      );

      // Select random attendance (50-80% of isibo users)
      const attendanceCount = Math.floor(isiboUsers.length * (0.5 + Math.random() * 0.3));
      const shuffledUsers = [...isiboUsers].sort(() => 0.5 - Math.random());
      const attendanceUsers = shuffledUsers.slice(0, attendanceCount);

      const report = reportRepo.create({
        activity: { id: task.activity.id } as Activity,
        task: { id: task.id } as Task,
        comment: `Report for ${taskTitle}. The activity was successfully completed with good community participation.`,
        materialsUsed: ['Community materials', 'Local resources', 'Volunteer time'],
        challengesFaced: 'Weather conditions and resource limitations',
        suggestions: 'Increase community awareness and improve resource allocation',
        evidenceUrls: [`https://example.com/evidence/${reportCount + 1}.jpg`],
      });

      const savedReport = await reportRepo.save(report);

      // Set attendance separately
      savedReport.attendance = attendanceUsers;
      await reportRepo.save(savedReport);

      reportCount++;
      console.log(`   ✓ Created report for: ${taskTitle}`);
    }

    console.log(`✅ Created ${reportCount} reports`);

    // Summary
    console.log('\n🎉 Database seeding completed successfully!');
    console.log('📊 Summary:');
    console.log(`   • Real Rwanda location data seeded`);
    console.log(`   • ${cellVillages.length} villages from ${gihangaVillage.cell.name} cell`);
    console.log(`   • ${isibos.size} isibos (10 per village)`);
    console.log(`   • ${houses.size} houses (10 per isibo)`);
    console.log(`   • ${users.size} citizens (3 per house)`);
    console.log(`   • ${activities.size} activities`);
    console.log(`   • ${tasks.size} tasks`);
    console.log(`   • ${reportCount} reports`);
    console.log('\n✨ All data has been seeded using real Rwanda locations with Gihanga village\'s cell!');

  } catch (error) {
    console.error('❌ Error during seeding:', error);
    throw error;
  } finally {
    await app.close();
  }
}

// Run the seeding
if (require.main === module) {
  seed()
    .then(() => {
      console.log('🌱 Seeding completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Seeding failed:', error);
      process.exit(1);
    });
}
