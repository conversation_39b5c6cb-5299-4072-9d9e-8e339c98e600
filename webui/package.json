{"name": "webui", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@aws-sdk/client-s3": "^3.817.0", "@aws-sdk/s3-request-presigner": "^3.817.0", "@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@radix-ui/react-alert-dialog": "^1.1.13", "@radix-ui/react-avatar": "^1.1.7", "@radix-ui/react-checkbox": "^1.2.3", "@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-dropdown-menu": "^2.1.12", "@radix-ui/react-label": "^2.1.4", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-select": "^2.2.4", "@radix-ui/react-separator": "^1.1.4", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-tabs": "^1.1.11", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-toggle": "^1.1.6", "@radix-ui/react-toggle-group": "^1.1.7", "@radix-ui/react-tooltip": "^1.2.6", "@tabler/icons-react": "^3.31.0", "@tanstack/react-table": "^8.21.3", "@types/jspdf": "^1.3.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "dotenv": "^16.5.0", "html2canvas": "^1.4.1", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "jwt-decode": "^4.0.0", "lucide-react": "^0.503.0", "next": "15.3.1", "next-themes": "^0.4.6", "react": "^19.0.0", "react-day-picker": "^9.8.0", "react-dom": "^19.0.0", "recharts": "^2.15.3", "sonner": "^2.0.3", "tailwind-merge": "^3.2.0", "vaul": "^1.1.2", "zod": "^3.24.3"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.1", "tailwindcss": "^4", "tw-animate-css": "^1.2.8", "typescript": "^5"}}