import { Module } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import { UsersModule } from "../users/users.module";
import { User } from "../users/entities/user.entity";
import { CellsController } from "./cells.controller";
import { CellsService } from "./cells.service";
import { Cell } from "./entities/cell.entity";
import { District } from "./entities/district.entity";
import { House } from "./entities/house.entity";
import { Isibo } from "./entities/isibo.entity";
import { Province } from "./entities/province.entity";
import { Sector } from "./entities/sector.entity";
import { Village } from "./entities/village.entity";
import { HousesController } from "./houses.controller";
import { HousesService } from "./houses.service";
import { IsibosController } from "./isibos.controller";
import { IsibosService } from "./isibos.service";
import { VillagesController } from "./villages.controller";
import { VillagesService } from "./villages.service";
@Module({
  imports: [
    TypeOrmModule.forFeature([
      Province,
      District,
      Sector,
      Cell,
      Village,
      Isibo,
      House,
      User,
    ]),
    UsersModule,
  ],
  controllers: [
    CellsController,
    VillagesController,
    IsibosController,
    HousesController,
  ],
  providers: [CellsService, VillagesService, IsibosService, HousesService],
  exports: [CellsService, VillagesService, IsibosService, HousesService],
})
export class LocationsModule {}
