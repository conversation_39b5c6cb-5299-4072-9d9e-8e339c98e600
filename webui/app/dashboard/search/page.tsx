"use client";

import { useState, useEffect } from "react";
import { <PERSON>, <PERSON>Content, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { 
  Search, 
  Activity, 
  CheckSquare, 
  FileText, 
  Users, 
  MapPin,
  Filter,
  Clock,
  TrendingUp
} from "lucide-react";
import { SearchResults, SearchResult } from "@/components/search/search-results";
import { searchAPI, GlobalSearchParams, GlobalSearchResponse } from "@/lib/api/search";
import { useToast } from "@/hooks/use-toast";

export default function GlobalSearchPage() {
  const [searchQuery, setSearchQuery] = useState("");
  const [searchResults, setSearchResults] = useState<GlobalSearchResponse | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedEntities, setSelectedEntities] = useState<string[]>(['all']);
  const [currentPage, setCurrentPage] = useState(1);
  const [activeTab, setActiveTab] = useState("all");
  const { toast } = useToast();

  const entityOptions = [
    { value: 'all', label: 'All', icon: <Search className="h-4 w-4" /> },
    { value: 'activities', label: 'Activities', icon: <Activity className="h-4 w-4" /> },
    { value: 'tasks', label: 'Tasks', icon: <CheckSquare className="h-4 w-4" /> },
    { value: 'reports', label: 'Reports', icon: <FileText className="h-4 w-4" /> },
    { value: 'users', label: 'Users', icon: <Users className="h-4 w-4" /> },
    { value: 'locations', label: 'Locations', icon: <MapPin className="h-4 w-4" /> },
  ];

  const handleSearch = async () => {
    if (!searchQuery.trim()) {
      toast({
        title: "Search query required",
        description: "Please enter a search term",
        variant: "destructive",
      });
      return;
    }

    setIsLoading(true);
    try {
      const params: GlobalSearchParams = {
        q: searchQuery,
        page: currentPage,
        size: 20,
        entities: selectedEntities.includes('all') ? ['all'] : selectedEntities,
      };

      const results = await searchAPI.globalSearch(params);
      setSearchResults(results);
    } catch (error) {
      console.error('Search error:', error);
      toast({
        title: "Search failed",
        description: "An error occurred while searching. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleEntityToggle = (entity: string) => {
    if (entity === 'all') {
      setSelectedEntities(['all']);
    } else {
      setSelectedEntities(prev => {
        const newEntities = prev.filter(e => e !== 'all');
        if (newEntities.includes(entity)) {
          const filtered = newEntities.filter(e => e !== entity);
          return filtered.length === 0 ? ['all'] : filtered;
        } else {
          return [...newEntities, entity];
        }
      });
    }
  };

  const getAllResults = (): SearchResult[] => {
    if (!searchResults) return [];
    
    return [
      ...searchResults.results.activities.map(r => ({ ...r, type: 'activity' as const })),
      ...searchResults.results.tasks.map(r => ({ ...r, type: 'task' as const })),
      ...searchResults.results.reports.map(r => ({ ...r, type: 'report' as const })),
      ...searchResults.results.users.map(r => ({ ...r, type: 'user' as const })),
      ...searchResults.results.locations.map(r => ({ ...r, type: 'location' as const })),
    ].sort((a, b) => (b.relevanceScore || 0) - (a.relevanceScore || 0));
  };

  const getTabResults = (tab: string): SearchResult[] => {
    if (!searchResults) return [];
    
    switch (tab) {
      case 'activities':
        return searchResults.results.activities.map(r => ({ ...r, type: 'activity' as const }));
      case 'tasks':
        return searchResults.results.tasks.map(r => ({ ...r, type: 'task' as const }));
      case 'reports':
        return searchResults.results.reports.map(r => ({ ...r, type: 'report' as const }));
      case 'users':
        return searchResults.results.users.map(r => ({ ...r, type: 'user' as const }));
      case 'locations':
        return searchResults.results.locations.map(r => ({ ...r, type: 'location' as const }));
      default:
        return getAllResults();
    }
  };

  const getResultCount = (type: string): number => {
    if (!searchResults) return 0;
    
    switch (type) {
      case 'activities':
        return searchResults.results.activities.length;
      case 'tasks':
        return searchResults.results.tasks.length;
      case 'reports':
        return searchResults.results.reports.length;
      case 'users':
        return searchResults.results.users.length;
      case 'locations':
        return searchResults.results.locations.length;
      default:
        return searchResults.totalResults;
    }
  };

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Global Search</h1>
          <p className="text-gray-600">Search across all activities, tasks, reports, users, and locations</p>
        </div>
      </div>

      {/* Search Form */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Search className="h-5 w-5" />
            Search
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-2">
            <div className="flex-1">
              <Input
                placeholder="Search for activities, tasks, reports, users, or locations..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
              />
            </div>
            <Button onClick={handleSearch} disabled={isLoading}>
              <Search className="h-4 w-4 mr-2" />
              Search
            </Button>
          </div>

          {/* Entity Filters */}
          <div className="space-y-2">
            <label className="text-sm font-medium">Search in:</label>
            <div className="flex flex-wrap gap-2">
              {entityOptions.map((option) => (
                <Button
                  key={option.value}
                  variant={selectedEntities.includes(option.value) ? "default" : "outline"}
                  size="sm"
                  onClick={() => handleEntityToggle(option.value)}
                  className="flex items-center gap-2"
                >
                  {option.icon}
                  {option.label}
                </Button>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Search Results */}
      {searchResults && (
        <div className="space-y-6">
          {/* Search Summary */}
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                  <div className="flex items-center gap-2">
                    <TrendingUp className="h-4 w-4 text-green-600" />
                    <span className="font-medium">{searchResults.totalResults} results found</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Clock className="h-4 w-4 text-gray-500" />
                    <span className="text-sm text-gray-600">
                      {searchResults.meta.searchTime}ms
                    </span>
                  </div>
                </div>
                <Badge variant="outline">
                  Query: "{searchResults.meta.query}"
                </Badge>
              </div>
            </CardContent>
          </Card>

          {/* Results Tabs */}
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-6">
              <TabsTrigger value="all" className="flex items-center gap-2">
                <Search className="h-4 w-4" />
                All ({searchResults.totalResults})
              </TabsTrigger>
              <TabsTrigger value="activities" className="flex items-center gap-2">
                <Activity className="h-4 w-4" />
                Activities ({getResultCount('activities')})
              </TabsTrigger>
              <TabsTrigger value="tasks" className="flex items-center gap-2">
                <CheckSquare className="h-4 w-4" />
                Tasks ({getResultCount('tasks')})
              </TabsTrigger>
              <TabsTrigger value="reports" className="flex items-center gap-2">
                <FileText className="h-4 w-4" />
                Reports ({getResultCount('reports')})
              </TabsTrigger>
              <TabsTrigger value="users" className="flex items-center gap-2">
                <Users className="h-4 w-4" />
                Users ({getResultCount('users')})
              </TabsTrigger>
              <TabsTrigger value="locations" className="flex items-center gap-2">
                <MapPin className="h-4 w-4" />
                Locations ({getResultCount('locations')})
              </TabsTrigger>
            </TabsList>

            <TabsContent value="all" className="mt-6">
              <SearchResults
                results={getAllResults()}
                isLoading={isLoading}
                totalResults={searchResults.totalResults}
                currentPage={searchResults.meta.currentPage}
                totalPages={searchResults.meta.totalPages}
                onPageChange={setCurrentPage}
              />
            </TabsContent>

            <TabsContent value="activities" className="mt-6">
              <SearchResults
                results={getTabResults('activities')}
                isLoading={isLoading}
                totalResults={getResultCount('activities')}
                currentPage={1}
                totalPages={1}
              />
            </TabsContent>

            <TabsContent value="tasks" className="mt-6">
              <SearchResults
                results={getTabResults('tasks')}
                isLoading={isLoading}
                totalResults={getResultCount('tasks')}
                currentPage={1}
                totalPages={1}
              />
            </TabsContent>

            <TabsContent value="reports" className="mt-6">
              <SearchResults
                results={getTabResults('reports')}
                isLoading={isLoading}
                totalResults={getResultCount('reports')}
                currentPage={1}
                totalPages={1}
              />
            </TabsContent>

            <TabsContent value="users" className="mt-6">
              <SearchResults
                results={getTabResults('users')}
                isLoading={isLoading}
                totalResults={getResultCount('users')}
                currentPage={1}
                totalPages={1}
              />
            </TabsContent>

            <TabsContent value="locations" className="mt-6">
              <SearchResults
                results={getTabResults('locations')}
                isLoading={isLoading}
                totalResults={getResultCount('locations')}
                currentPage={1}
                totalPages={1}
              />
            </TabsContent>
          </Tabs>
        </div>
      )}

      {/* Empty State */}
      {!searchResults && !isLoading && (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-12">
            <Search className="h-12 w-12 text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">Start searching</h3>
            <p className="text-gray-500 text-center">
              Enter a search term above to find activities, tasks, reports, users, and locations.
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
