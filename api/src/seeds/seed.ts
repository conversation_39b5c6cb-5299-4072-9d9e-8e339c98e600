import { NestFactory } from '@nestjs/core';
import { AppModule } from '../app.module';
import { DataSource } from 'typeorm';
import { seedData } from './seed-data';
import * as bcrypt from 'bcryptjs';

// Import entities
import { Province } from '../locations/entities/province.entity';
import { District } from '../locations/entities/district.entity';
import { Sector } from '../locations/entities/sector.entity';
import { Cell } from '../locations/entities/cell.entity';
import { Village } from '../locations/entities/village.entity';
import { Isibo } from '../locations/entities/isibo.entity';
import { House } from '../locations/entities/house.entity';
import { User } from '../users/entities/user.entity';
import { Activity } from '../activities/entities/activity.entity';
import { Task } from '../activities/entities/task.entity';
import { Report } from '../activities/entities/report.entity';

async function seed() {
  console.log('🌱 Starting database seeding...');
  
  const app = await NestFactory.createApplicationContext(AppModule);
  const dataSource = app.get(DataSource);

  try {
    // Clear existing data (in reverse order of dependencies)
    console.log('🧹 Clearing existing data...');
    await dataSource.query('DELETE FROM report_attendance');
    await dataSource.query('DELETE FROM reports');
    await dataSource.query('DELETE FROM tasks');
    await dataSource.query('DELETE FROM activities');

    // Clear leader references first to avoid foreign key constraints
    await dataSource.query('UPDATE isibos SET leader_id = NULL WHERE leader_id IS NOT NULL');
    await dataSource.query('UPDATE villages SET leader_id = NULL WHERE leader_id IS NOT NULL');
    await dataSource.query('UPDATE cells SET leader_id = NULL WHERE leader_id IS NOT NULL');

    // Now we can safely delete users (except admins)
    await dataSource.query('DELETE FROM users WHERE role != \'ADMIN\'');
    await dataSource.query('DELETE FROM houses');
    await dataSource.query('DELETE FROM isibos');
    await dataSource.query('DELETE FROM villages');
    await dataSource.query('DELETE FROM cells');
    await dataSource.query('DELETE FROM sectors');
    await dataSource.query('DELETE FROM districts');
    await dataSource.query('DELETE FROM provinces');

    // Get repositories
    const provinceRepo = dataSource.getRepository(Province);
    const districtRepo = dataSource.getRepository(District);
    const sectorRepo = dataSource.getRepository(Sector);
    const cellRepo = dataSource.getRepository(Cell);
    const villageRepo = dataSource.getRepository(Village);
    const isiboRepo = dataSource.getRepository(Isibo);
    const houseRepo = dataSource.getRepository(House);
    const userRepo = dataSource.getRepository(User);
    const activityRepo = dataSource.getRepository(Activity);
    const taskRepo = dataSource.getRepository(Task);
    const reportRepo = dataSource.getRepository(Report);

    // 1. Create Provinces
    console.log('🏛️ Creating provinces...');
    const provinces = new Map<string, Province>();
    for (const provinceData of seedData.provinces) {
      const province = provinceRepo.create({ name: provinceData.name });
      const savedProvince = await provinceRepo.save(province);
      provinces.set(provinceData.name, savedProvince);
      console.log(`   ✓ Created province: ${provinceData.name}`);
    }

    // 2. Create Districts
    console.log('🏘️ Creating districts...');
    const districts = new Map<string, District>();
    for (const districtData of seedData.districts) {
      const province = provinces.get(districtData.provinceName);
      if (!province) {
        throw new Error(`Province ${districtData.provinceName} not found`);
      }
      
      const district = districtRepo.create({
        name: districtData.name,
        province: province,
      });
      const savedDistrict = await districtRepo.save(district);
      districts.set(districtData.name, savedDistrict);
      console.log(`   ✓ Created district: ${districtData.name}`);
    }

    // 3. Create Sectors
    console.log('🏞️ Creating sectors...');
    const sectors = new Map<string, Sector>();
    for (const sectorData of seedData.sectors) {
      const district = districts.get(sectorData.districtName);
      if (!district) {
        throw new Error(`District ${sectorData.districtName} not found`);
      }
      
      const sector = sectorRepo.create({
        name: sectorData.name,
        district: district,
      });
      const savedSector = await sectorRepo.save(sector);
      sectors.set(sectorData.name, savedSector);
      console.log(`   ✓ Created sector: ${sectorData.name}`);
    }

    // 4. Create Cells
    console.log('🏠 Creating cells...');
    const cells = new Map<string, Cell>();
    for (const cellData of seedData.cells) {
      const sector = sectors.get(cellData.sectorName);
      if (!sector) {
        throw new Error(`Sector ${cellData.sectorName} not found`);
      }
      
      const cell = cellRepo.create({
        name: cellData.name,
        sector: sector,
      });
      const savedCell = await cellRepo.save(cell);
      cells.set(cellData.name, savedCell);
      console.log(`   ✓ Created cell: ${cellData.name}`);
    }

    // 5. Create Villages
    console.log('🏘️ Creating villages...');
    const villages = new Map<string, Village>();
    for (const villageData of seedData.villages) {
      const cell = cells.get(villageData.cellName);
      if (!cell) {
        throw new Error(`Cell ${villageData.cellName} not found`);
      }
      
      const village = villageRepo.create({
        name: villageData.name,
        cell: cell,
      });
      const savedVillage = await villageRepo.save(village);
      villages.set(villageData.name, savedVillage);
      console.log(`   ✓ Created village: ${villageData.name}`);
    }

    // 6. Create Isibos
    console.log('🏘️ Creating isibos...');
    const isibos = new Map<string, Isibo>();
    for (const isiboData of seedData.isibos) {
      const village = villages.get(isiboData.villageName);
      if (!village) {
        throw new Error(`Village ${isiboData.villageName} not found`);
      }
      
      const isibo = isiboRepo.create({
        name: isiboData.name,
        village: village,
      });
      const savedIsibo = await isiboRepo.save(isibo);
      isibos.set(isiboData.name, savedIsibo);
      console.log(`   ✓ Created isibo: ${isiboData.name}`);
    }

    // 7. Create Houses
    console.log('🏠 Creating houses...');
    const houses = new Map<string, House>();
    for (const houseData of seedData.houses) {
      const isibo = isibos.get(houseData.isiboName);
      if (!isibo) {
        throw new Error(`Isibo ${houseData.isiboName} not found`);
      }
      
      const house = houseRepo.create({
        code: houseData.code,
        address: houseData.address,
        isibo: isibo,
      });
      const savedHouse = await houseRepo.save(house);
      houses.set(houseData.code, savedHouse);
      console.log(`   ✓ Created house: ${houseData.code}`);
    }

    // 8. Create Citizens
    console.log('👥 Creating citizens...');
    const users = new Map<string, User>();
    for (const citizenData of seedData.citizens) {
      const village = villages.get(citizenData.villageName);
      const isibo = isibos.get(citizenData.isiboName);
      const house = houses.get(citizenData.houseCode);
      const cell = cells.get('UBUMWE'); // All citizens are in the same cell for this seed
      
      if (!village || !isibo || !house || !cell) {
        console.error(`Missing references for citizen ${citizenData.names}`);
        continue;
      }
      
      const hashedPassword = await bcrypt.hash(citizenData.password, 10);
      
      const user = userRepo.create({
        names: citizenData.names,
        email: citizenData.email,
        phone: citizenData.phone,
        password: hashedPassword,
        role: citizenData.role,
        village: village,
        isibo: isibo,
        house: house,
        cell: cell,
      });
      
      const savedUser = await userRepo.save(user);
      users.set(citizenData.email, savedUser);
      console.log(`   ✓ Created citizen: ${citizenData.names}`);
    }

    console.log(`✅ Created ${users.size} citizens`);

    // 9. Create Activities
    console.log('🎯 Creating activities...');
    const activities = new Map<string, Activity>();
    for (const activityData of seedData.activities) {
      const village = villages.get(activityData.villageName);
      if (!village) {
        throw new Error(`Village ${activityData.villageName} not found`);
      }
      
      const activity = activityRepo.create({
        title: activityData.title,
        description: activityData.description,
        date: activityData.date,
        village: village,
      });
      
      const savedActivity = await activityRepo.save(activity);
      activities.set(activityData.title, savedActivity);
      console.log(`   ✓ Created activity: ${activityData.title}`);
    }

    console.log(`✅ Created ${activities.size} activities`);

    // 10. Create Tasks
    console.log('📋 Creating tasks...');
    const tasks = new Map<string, Task>();
    for (const taskData of seedData.tasks) {
      const activity = activities.get(taskData.activityTitle);
      const isibo = isibos.get(taskData.isiboName);

      if (!activity || !isibo) {
        console.error(`Missing references for task ${taskData.title}`);
        continue;
      }

      const task = taskRepo.create({
        title: taskData.title,
        description: taskData.description,
        status: taskData.status,
        estimatedCost: taskData.estimatedCost,
        actualCost: taskData.actualCost,
        expectedParticipants: taskData.expectedParticipants,
        actualParticipants: taskData.actualParticipants,
        expectedFinancialImpact: taskData.expectedFinancialImpact,
        actualFinancialImpact: taskData.actualFinancialImpact,
        activity: activity,
        isibo: isibo,
      });

      const savedTask = await taskRepo.save(task);
      tasks.set(taskData.title, savedTask);
      console.log(`   ✓ Created task: ${taskData.title}`);
    }

    console.log(`✅ Created ${tasks.size} tasks`);

    // 11. Create Reports
    console.log('📊 Creating reports...');
    let reportCount = 0;
    for (const reportData of seedData.reports) {
      const activity = activities.get(reportData.activityTitle);
      const task = tasks.get(reportData.taskTitle);

      if (!activity || !task) {
        console.error(`Missing references for report ${reportData.taskTitle}`);
        continue;
      }

      // Get attendance users
      const attendanceUsers = reportData.attendanceEmails
        .map(email => users.get(email))
        .filter(user => user !== undefined);

      const report = reportRepo.create({
        activity: activity,
        task: task,
        comment: reportData.comment,
        materialsUsed: reportData.materialsUsed,
        challengesFaced: reportData.challengesFaced,
        suggestions: reportData.suggestions,
        evidenceUrls: reportData.evidenceUrls,
        attendance: attendanceUsers,
      });

      await reportRepo.save(report);
      reportCount++;
      console.log(`   ✓ Created report for: ${reportData.taskTitle}`);
    }

    console.log(`✅ Created ${reportCount} reports`);

    // Summary
    console.log('\n🎉 Database seeding completed successfully!');
    console.log('📊 Summary:');
    console.log(`   • ${seedData.provinces.length} provinces`);
    console.log(`   • ${seedData.districts.length} districts`);
    console.log(`   • ${seedData.sectors.length} sectors`);
    console.log(`   • ${seedData.cells.length} cells`);
    console.log(`   • ${seedData.villages.length} villages`);
    console.log(`   • ${seedData.isibos.length} isibos`);
    console.log(`   • ${seedData.houses.length} houses`);
    console.log(`   • ${seedData.citizens.length} citizens`);
    console.log(`   • ${seedData.activities.length} activities`);
    console.log(`   • ${seedData.tasks.length} tasks`);
    console.log(`   • ${reportCount} reports`);
    console.log('\n✨ All data has been seeded with realistic relationships and dates between January-June 2025!');

  } catch (error) {
    console.error('❌ Error during seeding:', error);
    throw error;
  } finally {
    await app.close();
  }
}

// Run the seeding
if (require.main === module) {
  seed()
    .then(() => {
      console.log('🌱 Seeding completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Seeding failed:', error);
      process.exit(1);
    });
}
