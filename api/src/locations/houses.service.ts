import {
  BadRequestException,
  ConflictException,
  Injectable,
  NotFoundException,
} from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { paginate } from "nestjs-typeorm-paginate";
import { Not, Repository } from "typeorm";
import { UsersService } from "../users/users.service";
import { CreateHouseDto } from "./dto/create-house.dto";
import { UpdateHouseDto } from "./dto/update-house.dto";
import { House } from "./entities/house.entity";
import { Isibo } from "./entities/isibo.entity";
import { Citizen } from "./entities/citizen.entity";
import { CreateCitizenDTO } from "src/users/dto/create-citizen.dto";
import { Village } from "./entities/village.entity";
import { UserRole } from "src/__shared__/enums/user-role.enum";
import { FetchHouseDto } from "./dto/fetch-house.dto";
import { User } from "../users/entities/user.entity";

@Injectable()
export class HousesService {
  constructor(
    @InjectRepository(Village)
    private readonly villageRepository: Repository<Village>,
    @InjectRepository(Isibo)
    private readonly isiboRepository: Repository<Isibo>,
    @InjectRepository(House)
    private readonly houseRepository: Repository<House>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    private readonly usersService: UsersService,
  ) {}

  private async validateHouseCode(
    isiboId: string,
    code: string,
  ): Promise<void> {
    const existingHouse = await this.houseRepository.findOne({
      where: {
        isibo: { id: Not(isiboId) },
        code,
      },
    });

    if (existingHouse) {
      throw new ConflictException(
        `House with code ${code} already exists in this isibo`,
      );
    }
  }

  async createHouse(createHouseDto: CreateHouseDto.Input): Promise<House> {
    // Validate isiboId
    const isibo = await this.isiboRepository.findOne({
      where: { id: createHouseDto.isiboId },
      relations: [
        "leader",
        "village",
        "village.users",
        "village.cell",
        "village.cell.users",
      ],
    });

    if (!isibo) {
      throw new NotFoundException("Isibo not found");
    }

    await this.validateHouseCode(createHouseDto.isiboId, createHouseDto.code);

    const house = this.houseRepository.create({
      code: createHouseDto.code,
      address: createHouseDto.address,
      isibo: { id: createHouseDto.isiboId },
    });

    // Save the house first to get the ID
    const savedHouse = await this.houseRepository.save(house);

    // Assign members if provided
    if (createHouseDto.members && createHouseDto.members.length > 0) {
      const memberIds = await this.createCitizensAndGetIds(
        createHouseDto.members,
        isibo.village.id,
        isibo.id,
        savedHouse.id,
      );
      await this.assignMembersToHouse(savedHouse.id, memberIds);
    }

    // Fetch the house with members to return the complete data
    const houseWithMembers = await this.houseRepository.findOne({
      where: { id: savedHouse.id },
      relations: ["isibo", "members"],
    });

    if (!houseWithMembers) {
      throw new NotFoundException("House not found after creation");
    }

    return houseWithMembers;
  }

  async updateHouse(
    id: string,
    updateHouseDto: UpdateHouseDto.Input,
  ): Promise<House> {
    const house = await this.houseRepository.findOne({
      where: { id },
      relations: [
        "isibo",
        "isibo.leader",
        "isibo.village",
        "isibo.village.users",
        "isibo.village.cell",
        "isibo.village.cell.users",
        "members",
      ],
    });

    if (!house) {
      throw new NotFoundException("House not found");
    }

    if (updateHouseDto.code) {
      await this.validateHouseCode(house.isibo.id, updateHouseDto.code);
      house.code = updateHouseDto.code;
    }

    if (updateHouseDto.address) {
      house.address = updateHouseDto.address;
    }

    // Handle member assignment - only add new members
    if (updateHouseDto.memberIds && updateHouseDto.memberIds.length > 0) {
      // Add existing citizens to the house (only add, don't remove)
      await this.addMembersToHouse(house.id, updateHouseDto.memberIds);
    }

    if (updateHouseDto.members && updateHouseDto.members.length > 0) {
      // Create new citizens and assign them to the house
      const memberIds = await this.createCitizensAndGetIds(
        updateHouseDto.members,
        house.isibo.village.id,
        house.isibo.id,
        house.id,
      );
      await this.addMembersToHouse(house.id, memberIds);
    }

    const savedHouse = await this.houseRepository.save(house);

    // Fetch the house with members to return the complete data
    const houseWithMembers = await this.houseRepository.findOne({
      where: { id: savedHouse.id },
      relations: ["isibo", "members"],
    });

    if (!houseWithMembers) {
      throw new NotFoundException("House not found after update");
    }

    return houseWithMembers;
  }

  async deleteHouse(id: string): Promise<void> {
    const house = await this.houseRepository.findOne({
      where: { id },
      relations: [
        "isibo",
        "isibo.leader",
        "isibo.village",
        "isibo.village.users",
        "isibo.village.cell",
        "isibo.village.cell.users",
        "members",
      ],
    });

    if (!house) {
      throw new NotFoundException("House not found");
    }

    // Delete all members of the house
    if (house.members && house.members.length > 0) {
      for (const member of house.members) {
        try {
          await this.usersService.deleteUser(member.id);
        } catch (error) {
          console.error(`Failed to delete member ${member.id}:`, error);
        }
      }
    }

    await this.houseRepository.softDelete(id);
  }

  async findAllHouses(dto: FetchHouseDto.Input): Promise<FetchHouseDto.Output> {
    const queryBuilder = this.houseRepository
      .createQueryBuilder("house")
      .leftJoin("house.isibo", "isibo")
      .leftJoinAndSelect("house.members", "members")
      .where("isibo.id = :isiboId", { isiboId: dto.isiboId });

    if (dto.q) {
      queryBuilder.andWhere("house.code ILIKE :search", {
        search: `%${dto.q}%`,
      });
    }

    const result = await paginate(queryBuilder, {
      page: dto.page,
      limit: dto.size,
    });

    return {
      items: result.items || [],
      meta: {
        totalItems: result.meta.totalItems || 0,
        itemCount: result.meta.itemCount || 0,
        itemsPerPage: result.meta.itemsPerPage || dto.size,
        totalPages: result.meta.totalPages || 0,
        currentPage: result.meta.currentPage || dto.page,
      },
    };
  }

  async findHouseById(id: string): Promise<House> {
    const house = await this.houseRepository.findOne({
      where: { id },
      relations: ["isibo", "members"],
    });

    if (!house) {
      throw new NotFoundException("House not found");
    }

    return house;
  }

  private generateRandomPassword(): string {
    const length = 12;
    const charset =
      "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*";
    let password = "";
    for (let i = 0; i < length; i++) {
      const randomIndex = Math.floor(Math.random() * charset.length);
      password += charset[randomIndex];
    }
    return password;
  }

  private async createCitizensAndGetIds(
    citizens: Citizen[],
    villageId: string,
    isiboId: string,
    houseId: string,
  ): Promise<string[]> {
    console.log(
      `Creating ${citizens.length} new citizens for house ${houseId}`,
    );
    const userIds: string[] = [];

    const village = await this.villageRepository.findOne({
      where: { id: villageId },
      relations: ["cell"],
    });

    if (!village) {
      throw new NotFoundException("Village not found");
    }

    for (const citizen of citizens) {
      try {
        console.log(`Creating citizen: ${citizen.email}`);

        const existingUser = await this.usersService.findUserByEmail(
          citizen.email,
        );
        if (existingUser) {
          throw new ConflictException(
            `User with email ${citizen.email} already exists`,
          );
        }

        const citizenData: CreateCitizenDTO.Input = {
          names: citizen.names,
          email: citizen.email,
          phone: citizen.phone,
          password: this.generateRandomPassword(),
          cellId: village.cell.id,
          villageId: villageId,
          isiboId: isiboId,
          houseId: houseId,
        };

        await this.usersService.createCitizen(citizenData);

        const createdUser = await this.usersService.findUserByEmail(
          citizen.email,
        );
        if (createdUser) {
          userIds.push(createdUser.id);
          console.log(
            `Successfully created citizen: ${citizen.email} (ID: ${createdUser.id})`,
          );
        } else {
          throw new Error(`Failed to retrieve created user: ${citizen.email}`);
        }
      } catch (error) {
        console.error(`Failed to create citizen ${citizen.email}:`, error);
        throw new BadRequestException(
          `Failed to create citizen ${citizen.email}: ${error.message}`,
        );
      }
    }

    console.log(
      `Successfully created ${userIds.length} citizens for house ${houseId}`,
    );
    return userIds;
  }

  async assignMembersToHouse(
    houseId: string,
    memberIds: string[],
  ): Promise<void> {
    console.log(`=== Starting member assignment for house ${houseId} ===`);
    console.log(
      `Target member IDs: ${memberIds.length > 0 ? memberIds.join(", ") : "none (empty house)"}`,
    );

    // First, remove all current members from this house
    console.log(`Step 1: Removing all existing members from house ${houseId}`);
    await this.usersService.removeHouseFromUsers(houseId);
    console.log(
      `Step 1 completed: Removed all existing members from house ${houseId}`,
    );

    // If no new members to assign, we're done (house is now empty)
    if (memberIds.length === 0) {
      console.log(
        `Step 2: No new members to assign - house ${houseId} is now empty`,
      );
      console.log(`=== Member assignment completed for house ${houseId} ===`);
      return;
    }

    // Validate and assign new members
    console.log(`Step 2: Validating ${memberIds.length} member IDs`);
    const users = await this.usersService.findUsersByIds(memberIds);

    if (users.length !== memberIds.length) {
      const foundIds = users.map((user) => user.id);
      const missingIds = memberIds.filter((id) => !foundIds.includes(id));
      throw new NotFoundException(
        `One or more member users not found: ${missingIds.join(", ")}`,
      );
    }

    const nonCitizens = users.filter((user) => user.role !== UserRole.CITIZEN);
    if (nonCitizens.length > 0) {
      const nonCitizenNames = nonCitizens.map(
        (user) => `${user.names} (${user.role})`,
      );
      throw new BadRequestException(
        `All members must have CITIZEN role. Found non-citizens: ${nonCitizenNames.join(", ")}`,
      );
    }

    console.log(
      `Step 3: Assigning ${memberIds.length} validated users to house ${houseId}`,
    );
    // Assign the validated users to the house
    await this.usersService.assignUsersToHouse(memberIds, houseId);
    console.log(
      `Step 3 completed: Successfully assigned ${memberIds.length} members to house ${houseId}`,
    );
    console.log(`=== Member assignment completed for house ${houseId} ===`);
  }

  async addMembersToHouse(houseId: string, memberIds: string[]): Promise<void> {
    console.log(`Adding ${memberIds.length} members to house ${houseId}`);

    if (memberIds.length === 0) {
      console.log(`No members to add to house ${houseId}`);
      return;
    }

    // Validate that all users exist
    const users = await this.usersService.findUsersByIds(memberIds);

    if (users.length !== memberIds.length) {
      const foundIds = users.map((user) => user.id);
      const missingIds = memberIds.filter((id) => !foundIds.includes(id));
      throw new NotFoundException(`Users not found: ${missingIds.join(", ")}`);
    }

    // Add users to the house (this will only add, not remove existing ones)
    await this.usersService.assignUsersToHouse(memberIds, houseId);

    console.log(
      `Successfully added ${memberIds.length} members to house ${houseId}`,
    );
  }

  async removeMemberFromHouse(houseId: string, userId: string): Promise<void> {
    console.log(`=== Starting member removal for house ${houseId} ===`);
    console.log(`Removing user ${userId} from house ${houseId}`);

    // Verify the user exists and is in the house
    const user = await this.userRepository.findOne({
      where: { id: userId },
      relations: ["house"],
    });

    if (!user) {
      console.log(`ERROR: User with ID ${userId} not found`);
      throw new NotFoundException(`User with ID ${userId} not found`);
    }

    console.log(`Found user: ${user.names} (${user.email})`);
    console.log(`User's current house: ${user.house ? user.house.id : "none"}`);

    if (!user.house || user.house.id !== houseId) {
      console.log(`ERROR: User ${userId} is not a member of house ${houseId}`);
      throw new NotFoundException(
        `User ${userId} is not a member of house ${houseId}`,
      );
    }

    console.log(
      `User is confirmed to be in house ${houseId}, proceeding with removal`,
    );

    // Remove only this specific user from the house
    const result = await this.userRepository
      .createQueryBuilder()
      .update()
      .set({ house: null })
      .where("id = :userId", { userId })
      .execute();

    console.log(`Database update result: ${result.affected} rows affected`);

    // Verify the removal worked
    const updatedUser = await this.userRepository.findOne({
      where: { id: userId },
      relations: ["house"],
    });

    if (updatedUser && updatedUser.house) {
      console.log(
        `WARNING: User still has house after removal: ${updatedUser.house.id}`,
      );
    } else {
      console.log(`SUCCESS: User house has been set to null`);
    }

    console.log(`=== Member removal completed for house ${houseId} ===`);
  }
}
